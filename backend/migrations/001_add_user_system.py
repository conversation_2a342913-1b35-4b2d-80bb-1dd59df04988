#!/usr/bin/env python3
"""
Migration script to add user system to existing TagTok database.
This script will:
1. Create a backup of the existing database
2. Add new user management tables
3. Create a default admin user
4. Preserve all existing video data
"""

import os
import sys
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from models.database import create_tables, SessionLocal, User, Video
from sqlalchemy.orm import Session
from passlib.context import CryptContext

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def backup_database(db_path: str) -> str:
    """Create a backup of the existing database"""
    if not os.path.exists(db_path):
        print(f"Database file {db_path} does not exist. Creating new database.")
        return None
    
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(db_path, backup_path)
    print(f"Database backed up to: {backup_path}")
    return backup_path

def create_default_admin_user(db: Session) -> User:
    """Create a default admin user"""
    # Check if any users exist
    existing_user = db.query(User).first()
    if existing_user:
        print("Users already exist in the database. Skipping admin user creation.")
        return existing_user
    
    # Create default admin user
    hashed_password = pwd_context.hash("admin123")  # Change this in production!
    admin_user = User(
        username="admin",
        email="<EMAIL>",
        hashed_password=hashed_password,
        full_name="TagTok Administrator",
        is_active=True,
        is_superuser=True
    )
    
    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    
    print(f"Created default admin user: {admin_user.username}")
    print("Default password: admin123 (CHANGE THIS IN PRODUCTION!)")
    return admin_user

def assign_videos_to_admin(db: Session, admin_user: User):
    """Assign all existing videos to the admin user"""
    videos_without_owner = db.query(Video).filter(Video.user_id.is_(None)).all()
    
    if not videos_without_owner:
        print("No videos without owners found.")
        return
    
    for video in videos_without_owner:
        video.user_id = admin_user.id
    
    db.commit()
    print(f"Assigned {len(videos_without_owner)} videos to admin user")

def run_migration():
    """Run the complete migration"""
    print("Starting TagTok user system migration...")
    
    # Get database path from environment or use default
    db_path = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")
    if db_path.startswith("sqlite:///"):
        db_file_path = db_path.replace("sqlite:///", "")
    else:
        print(f"Unsupported database URL: {db_path}")
        return False
    
    # Create backup
    backup_path = backup_database(db_file_path)
    
    try:
        # Create new tables
        print("Creating new database tables...")
        create_tables()
        
        # Create database session
        db = SessionLocal()
        
        try:
            # Create default admin user
            admin_user = create_default_admin_user(db)
            
            # Assign existing videos to admin
            assign_videos_to_admin(db, admin_user)
            
            print("Migration completed successfully!")
            print("\nNext steps:")
            print("1. Change the default admin password")
            print("2. Create additional user accounts as needed")
            print("3. Test the authentication system")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"Migration failed: {e}")
        if backup_path and os.path.exists(backup_path):
            print(f"Restoring from backup: {backup_path}")
            shutil.copy2(backup_path, db_file_path)
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
