#version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_EXTERNAL_PORT:-8080}:${BACKEND_INTERNAL_PORT:-8000}"
    volumes:
      - ./backend:/app
      - ./data/videos:/app/videos
      - ./data/db:/app/db
      - ./data/transcripts:/app/transcripts
      - ./data/cookies:/app/cookies
    environment:
      - DATABASE_URL=${DATABASE_URL:-sqlite:///db/tagTok.db}
      - VIDEOS_DIR=${VIDEOS_DIR:-/app/videos}
      - TRANSCRIPTS_DIR=${TRANSCRIPTS_DIR:-/app/transcripts}
      - PYTHONPATH=${PYTHONPATH:-/app}
      - BACKEND_INTERNAL_PORT=${BACKEND_INTERNAL_PORT:-8000}
      - OLLAMA_URL=${OLLAMA_URL:-http://ollama:11434}
      - WHISPER_MODEL_SIZE=${WHISPER_MODEL_SIZE:-base}
      - YTDLP_COOKIES_FILE=${YTDLP_COOKIES_FILE}
      - YTDLP_COOKIES_FROM_BROWSER=${YTDLP_COOKIES_FROM_BROWSER}
    depends_on:
      - frontend
      - ollama
    networks:
      - tagTok-network
    restart: unless-stopped
    #devices:
    #  - /dev/dri:/dev/dri
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${BACKEND_INTERNAL_PORT:-8000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "${FRONTEND_EXTERNAL_PORT:-3001}:${FRONTEND_INTERNAL_PORT:-80}"
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL}
      - REACT_APP_BACKEND_EXTERNAL_PORT=${BACKEND_EXTERNAL_PORT:-8080}
      - REACT_APP_FRONTEND_EXTERNAL_PORT=${FRONTEND_EXTERNAL_PORT:-3001}
      - REACT_APP_NGINX_PORT=${NGINX_PORT:-8790}
    networks:
      - tagTok-network
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "${OLLAMA_EXTERNAL_PORT:-11435}:${OLLAMA_INTERNAL_PORT:-11434}"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_INTERNAL_PORT=${OLLAMA_INTERNAL_PORT:-11434}
    networks:
      - tagTok-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  ollama-setup:
    image: alpine:latest
    depends_on:
      ollama:
        condition: service_healthy
    networks:
      - tagTok-network
    command: >
      sh -c "
        echo 'Waiting for Ollama service to be ready...' &&
        sleep 10 &&
        echo 'Installing curl...' &&
        apk add --no-cache curl &&
        echo 'Pulling llama3.2:3b model via API...' &&
        curl -X POST http://ollama:11434/api/pull -H 'Content-Type: application/json' -d '{\"name\": \"llama3.2:3b\"}' &&
        echo 'Model pull request sent successfully!' &&
        echo 'Verifying model...' &&
        curl -s http://ollama:11434/api/tags | grep -q 'llama3.2:3b' && echo 'Model verified!' || echo 'Model verification failed'
      "
    restart: "no"

  nginx:
    image: nginx:alpine
    ports:
      - "${NGINX_PORT:-8790}:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./data/videos:/usr/share/nginx/html/videos
    environment:
      - NGINX_PORT=${NGINX_PORT:-8790}
      - BACKEND_INTERNAL_PORT=${BACKEND_INTERNAL_PORT:-8000}
      - FRONTEND_INTERNAL_PORT=${FRONTEND_INTERNAL_PORT:-80}
    depends_on:
      - backend
      - frontend
    networks:
      - tagTok-network
    restart: unless-stopped

volumes:
  videos:
  db:
  transcripts:
  ollama_data:

networks:
  tagTok-network:
    driver: bridge
